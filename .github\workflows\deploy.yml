name: Deploy FastAPI to EC2 with Nginx

on:
  push:
    branches:
      - production

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Code
      uses: actions/checkout@v3

    - name: Add SSH Key to Agent
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.PROD_EC2_SSH_KEY }}

    - name: Deploy to EC2 and Restart Services
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.PROD_EC2_USER }}@${{ secrets.PROD_EC2_HOST }} << 'EOF'
          cd /home/<USER>/Vsorts-2-tier-classification-and-chat-with-database || exit 1

          echo "Stashing local changes if any..."
          git stash || true

          echo "Pulling latest changes..."
          git pull origin production || exit 1

          echo "Activating virtual environment..."
          source venv/bin/activate || exit 1

          echo "Installing dependencies..."
          pip install -r requirements.txt || exit 1

          echo "Restarting FastAPI app..."
          sudo systemctl restart fastapi.service || exit 1

          echo "Reloading Nginx..."
          sudo nginx -t && sudo systemctl reload nginx || exit 1
        EOF
