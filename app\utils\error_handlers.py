from fastapi import HTTPException
from app.models.error_models import ErrorResponse, ErrorResponseWrapper

def create_error_response(
    status_code: int,
    error_key: str,
    detail: str,
    field: str = None,
    suggestion: str = None
) -> dict:
    """Create a standardized error response"""
    error_response = ErrorResponse(
        status_code=status_code,
        key=error_key,
        detail=detail,
        field=field,
        suggestion=suggestion
    )
    return ErrorResponseWrapper(error=error_response).model_dump()


def raise_http_exception(
    status_code: int,
    error_key: str,
    detail: str,
    field: str = None,
    suggestion: str = None
):
    """Raise an HTTPException with a standardized error response"""
    error_content = create_error_response(
        status_code=status_code,
        error_key=error_key,
        detail=detail,
        field=field,
        suggestion=suggestion
    )
    raise HTTPException(status_code=status_code, detail=error_content)


# Input validation errors (400 Bad Request)
def missing_field_error(field_name: str):
    """Error for missing required field"""
    return raise_http_exception(
        status_code=400,
        error_key="MISSING_REQUIRED_FIELD",
        detail=f"The required field '{field_name}' is missing",
        field=field_name,
        suggestion=f"Please provide a valid {field_name}"
    )


def empty_field_error(field_name: str):
    """Error for empty field value"""
    return raise_http_exception(
        status_code=400,
        error_key="EMPTY_FIELD",
        detail=f"The field '{field_name}' cannot be empty",
        field=field_name,
        suggestion=f"Please provide a non-empty value for {field_name}"
    )


def invalid_type_error(field_name: str, expected_type: str, received_type: str = None):
    """Error for invalid data type"""
    return raise_http_exception(
        status_code=400,
        error_key="INVALID_DATA_TYPE",
        detail=f"The field '{field_name}' must be of type {expected_type}" +
               (f", received {received_type}" if received_type else ""),
        field=field_name,
        suggestion=f"Please provide a valid {expected_type} for {field_name}"
    )


def invalid_format_error(field_name: str, format_description: str):
    """Error for invalid format"""
    return raise_http_exception(
        status_code=400,
        error_key="INVALID_FORMAT",
        detail=f"The field '{field_name}' has an invalid format",
        field=field_name,
        suggestion=f"Please provide a value in the format: {format_description}"
    )


def content_too_long_error(field_name: str, max_length: int):
    """Error for content exceeding maximum length"""
    return raise_http_exception(
        status_code=400,
        error_key="CONTENT_TOO_LONG",
        detail=f"The content in '{field_name}' exceeds the maximum length of {max_length} characters",
        field=field_name,
        suggestion=f"Please provide content with fewer than {max_length} characters"
    )


def content_too_short_error(field_name: str, min_length: int):
    """Error for content below minimum length"""
    return raise_http_exception(
        status_code=400,
        error_key="CONTENT_TOO_SHORT",
        detail=f"The content in '{field_name}' is shorter than the minimum length of {min_length} characters",
        field=field_name,
        suggestion=f"Please provide content with at least {min_length} characters"
    )


# Authentication and authorization errors (401, 403, 404)
def user_not_found_error(user_id: str):
    """Error for user not found"""
    return raise_http_exception(
        status_code=404,
        error_key="USER_NOT_FOUND",
        detail=f"User with ID '{user_id}' not found",
        field="user_id",
        suggestion="Please provide a valid user ID"
    )


# Classification errors (422 Unprocessable Entity)
def classification_error():
    """Error for unable to classify content"""
    return raise_http_exception(
        status_code=422,
        error_key="CLASSIFICATION_ERROR",
        detail="Unable to classify content into Cultural Proficiency Continuum",
        field="vignette",
        suggestion="Please provide a clearer vignette that aligns with the Cultural Proficiency Continuum"
    )


def no_class_match_error():
    """Error for content not matching any class"""
    return raise_http_exception(
        status_code=422,
        error_key="NO_CLASS_MATCH",
        detail="Content does not belong to any Cultural Proficiency Continuum class",
        field="vignette",
        suggestion="Please provide content that aligns with one of the Cultural Proficiency Continuum classes"
    )


# Service errors (500, 503)
def openai_api_error(error_message: str):
    """Error for OpenAI API issues"""
    return raise_http_exception(
        status_code=503,
        error_key="OPENAI_API_ERROR",
        detail=f"Error communicating with OpenAI API: {error_message}",
        suggestion="Please try again later. If the problem persists, contact support"
    )


def database_error(error_message: str = None):
    """Error for database issues"""
    detail = "An error occurred while accessing the database"
    if error_message:
        detail += f": {error_message}"

    return raise_http_exception(
        status_code=500,
        error_key="DATABASE_ERROR",
        detail=detail,
        suggestion="Please try again later. If the problem persists, contact support"
    )


def server_error():
    """General server error"""
    return raise_http_exception(
        status_code=500,
        error_key="SERVER_ERROR",
        detail="An unexpected error occurred on the server",
        suggestion="Please try again later. If the problem persists, contact support"
    )
