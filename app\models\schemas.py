from pydantic import BaseModel, Field, field_validator, ConfigDict

class ClassificationRequest(BaseModel):
    model_config = ConfigDict(extra='forbid')  # This will raise an error for extra fields
    
    vignette: str = Field(...)
    user_id: str = Field(...)

    @field_validator('user_id')
    def validate_user_id(cls, v):
        if v is None:
            raise ValueError('user_id cannot be None')
        return v

    @field_validator('vignette')
    def validate_vignette(cls, v):
        if v is None:
            raise ValueError('vignette cannot be None')
        return v
