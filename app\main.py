import time
from fastapi import <PERSON><PERSON><PERSON>, Request, status, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from app.api.routes import router
from app.models.error_models import ErrorResponse, ErrorResponseWrapper
from app.utils.logger import log_request, log_error
# from app.core.startup import setup_logging

# Initialize logging
# setup_logging()

app = FastAPI(title="Vsorts API", description="API for Cultural Proficiency Continuum classification")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Add logging middleware
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Only log API endpoint executions (skip static files, etc.)
        if not request.url.path.startswith('/api/') and not request.url.path == '/chat/':
            return await call_next(request)

        start_time = time.time()

        # Process the request
        try:
            response = await call_next(request)
            process_time = round((time.time() - start_time) * 1000)
            log_request(request, response, process_time)
            return response
        except Exception as e:
            process_time = round((time.time() - start_time) * 1000)
            log_error(e, request, f"Processing time: {process_time} ms")
            # Re-raise the exception to be handled by exception handlers
            raise

app.add_middleware(LoggingMiddleware)

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    # Log the exception
    log_error(exc, request)
    # Check if this is our custom error format
    if isinstance(exc.detail, dict) and "error" in exc.detail:
        # This is our custom error format
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )
    else:
        # Standard HTTPException
        error_response = ErrorResponse(
            status_code=exc.status_code,
            key="HTTP_ERROR",
            detail=str(exc.detail)
        )
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponseWrapper(error=error_response).model_dump()
        )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    # Log the validation error
    log_error(exc, request)
    errors = exc.errors()

    if errors:
        error = errors[0]  # Take only the first error
        field = error["loc"][-1] if error["loc"] else None

        error_response = ErrorResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            key="VALIDATION_ERROR",
            detail=error["msg"],
            field=field,
            suggestion=f"Please provide a valid value for {field}" if field else "Please check your request parameters"
        )
    else:
        error_response = ErrorResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            key="VALIDATION_ERROR",
            detail="Invalid request parameters",
            suggestion="Please check your request format"
        )


    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=ErrorResponseWrapper(error=error_response).model_dump()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    # Log the exception
    log_error(exc, request)
    error_response = ErrorResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        key="SERVER_ERROR",
        detail="An unexpected error occurred on the server",
        suggestion="Please try again later. If the problem persists, contact support"
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponseWrapper(error=error_response).model_dump()
    )


app.include_router(router)