from app.db.connection import get_db_connection
import logging
import json

def user_exists(user_id):
    try:
        db = get_db_connection()
        cursor = db.cursor()
        cursor.execute("SELECT id FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()
        return user is not None
    except Exception as e:
        logging.error(f"Database error: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def save_chat_to_db(user_id, chat_id, ai_copilot_id, new_message):
    try:
        db = get_db_connection()
        cursor = db.cursor(dictionary=True)
        select_query = """
        SELECT message FROM ai_copilot_chat_session
        WHERE userId = %s AND chat_id = %s AND ai_copilot_id = %s;
        """
        cursor.execute(select_query, (user_id, chat_id, ai_copilot_id))
        result = cursor.fetchone()
        existing_messages = json.loads(result["message"]) if result else []
        existing_messages.append(new_message)
        message_json = json.dumps(existing_messages)
        insert_query = """
        INSERT INTO ai_copilot_chat_session (userId, chat_id, ai_copilot_id, message)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE message = VALUES(message);
        """
        cursor.execute(insert_query, (user_id, chat_id, ai_copilot_id, message_json))
        db.commit()
    except Exception as e:
        logging.error(f"Error saving chat: {e}")
    finally:
        cursor.close()
        db.close()