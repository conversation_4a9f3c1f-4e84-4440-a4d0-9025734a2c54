import logging
import os
import json
from logging.handlers import RotatingFileHandler
# We don't need traceback anymore with the simplified logging
from datetime import datetime

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Configure the logger
logger = logging.getLogger('vsorts_app')
logger.setLevel(logging.INFO)  # Set to INFO to reduce noise

# Create a standard formatter for console output
standard_formatter = logging.Formatter(
    '[%(asctime)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Create a file handler for all logs
file_handler = RotatingFileHandler(
    os.path.join(logs_dir, 'app.log'),
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(standard_formatter)

# Add handlers to the logger
logger.addHandler(file_handler)

# Create a console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(standard_formatter)
logger.addHandler(console_handler)

def log_api_activity(timestamp=None, user_id=None, chat_id=None, request_type=None, response_type=None,
                  message=None, vignette=None, classification=None, error=None,
                  status_code=None, latency=None):  # status_code and latency kept for backward compatibility
    """
    Log API activity in a structured format for easy analysis

    Args:
        timestamp: Timestamp of the activity (default: current time)
        user_id: User ID
        chat_id: Chat ID
        request_type: Type of request (e.g., 'classification', 'chat')
        response_type: Type of response (e.g., 'success', 'error')
        status_code: HTTP status code
        message: Message or explanation
        vignette: The vignette text (will be truncated)
        classification: The classification result
        latency: Processing time in milliseconds
        error: Error details if applicable
    """
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Determine status (success/error)
    status = 'error' if error or (response_type and response_type.lower() == 'error') else 'success'

    # Format inputs - combine all input parameters into a single string
    inputs = []
    if user_id:
        inputs.append(f"user_id={user_id}")
    if chat_id:
        inputs.append(f"chat_id={chat_id}")
    if request_type:
        inputs.append(f"request={request_type}")
    if vignette:
        # Truncate long vignettes
        vig_str = vignette[:50] + ('...' if len(vignette) > 50 else '')
        inputs.append(f"vignette='{vig_str}'")

    inputs_str = ', '.join(inputs) if inputs else 'No inputs'

    # Format output or error message
    if response_type and response_type.lower() == 'error':
        # For errors, always use the message if available
        if message and message.strip():
            output_str = message[:300] + ('...' if len(message) > 300 else '')
        elif error:
            # Fallback to error object if message not provided
            output_str = str(error)[:300] + ('...' if len(str(error)) > 300 else '')
        else:
            # Last resort - should never happen with our updated error handling
            output_str = "500: Unknown error"
    elif classification:
        # For classification results
        output_str = f"Class: {classification}"
        if message and message.strip():
            output_str += f" - {message[:100]}{'...' if len(message) > 100 else ''}"
    elif message and message.strip():
        # For other success responses with a message
        output_str = message[:200] + ('...' if len(message) > 200 else '')
    else:
        # For success responses without a message
        output_str = "Success"

    # Create the log string in the exact format requested
    # [timestamp] [inputs] [status] [outputs]
    log_str = f"[{inputs_str}] [{status}] [{output_str}]"

    if status == 'error':
        logger.error(log_str)
    else:
        logger.info(log_str)

    return timestamp

def log_error(error, request=None, additional_info=None):  # additional_info is kept for backward compatibility
    """
    Log error in structured format
    """
    user_id = None
    chat_id = None
    vignette = None
    request_type = None

    # Try to extract information from the request
    if request:
        try:
            request_type = f"{request.method} {request.url.path}"

            # Try to parse request body for user_id and chat_id
            if hasattr(request, 'body') and request.body:
                try:
                    body = json.loads(request.body.decode('utf-8'))
                    user_id = body.get('user_id')
                    chat_id = body.get('chat_id')
                    vignette = body.get('vignette')
                except:
                    pass
        except Exception:  # Silently ignore extraction errors
            pass

    # Format the error message with status code if available
    error_message = str(error)
    status_code = getattr(error, 'status_code', 500)

    # If the error has a detail attribute that's a dict (like our custom errors)
    if hasattr(error, 'detail') and isinstance(error.detail, dict):
        error_message = f"{status_code}: {json.dumps(error.detail)[:200]}{'...' if len(json.dumps(error.detail)) > 200 else ''}"
    else:
        # Otherwise, just prepend the status code
        error_message = f"{status_code}: {error_message[:200]}{'...' if len(error_message) > 200 else ''}"

    # Use our simplified logging format
    return log_api_activity(
        user_id=user_id,
        chat_id=chat_id,
        request_type=request_type,
        response_type='error',
        message=error_message,
        vignette=vignette
    )

def log_request(request, response=None, processing_time=None):  # processing_time kept for backward compatibility
    """
    Log request in structured format
    """
    user_id = None
    chat_id = None
    vignette = None
    request_type = None

    # Try to extract information from the request
    try:
        request_type = f"{request.method} {request.url.path}"

        # Try to parse request body for user_id and chat_id
        if hasattr(request, 'body') and request.body:
            try:
                body = json.loads(request.body.decode('utf-8'))
                user_id = body.get('user_id')
                chat_id = body.get('chat_id')
                vignette = body.get('vignette')
            except:
                pass
    except Exception:  # Silently ignore extraction errors
        pass

    # Determine if this was a successful response
    is_success = True
    status_code = None
    if response:
        status_code = response.status_code
        is_success = status_code < 400

    # Get response body if available
    response_body = None
    if response and hasattr(response, 'body'):
        try:
            response_body = response.body.decode('utf-8')
            # For error responses, format the output with status code
            if not is_success and status_code:
                # Try to parse the JSON response
                try:
                    error_json = json.loads(response_body)
                    # Format the error output with status code
                    response_body = f"{status_code}: {json.dumps(error_json)[:200]}{'...' if len(json.dumps(error_json)) > 200 else ''}"
                except:
                    # If not JSON, just prepend the status code
                    response_body = f"{status_code}: {response_body[:200]}{'...' if len(response_body) > 200 else ''}"
        except:
            # If we can't decode the body, at least include the status code for errors
            if not is_success and status_code:
                response_body = f"{status_code}: Error response (body could not be decoded)"

    # Use our simplified logging format
    return log_api_activity(
        user_id=user_id,
        chat_id=chat_id,
        request_type=request_type,
        response_type='success' if is_success else 'error',
        message=response_body,
        vignette=vignette
    )

def log_classification(user_id, chat_id, vignette, v_class, explanation, latency=None):  # latency kept for backward compatibility
    """
    Log classification in structured format
    """
    # This is the main endpoint execution log
    return log_api_activity(
        user_id=user_id,
        chat_id=chat_id,
        request_type='classification',
        response_type='success',
        message=explanation,
        vignette=vignette,
        classification=v_class
    )
