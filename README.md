# FastAPI Chatbot with OpenAI Integration

This FastAPI application provides a chatbot that integrates with OpenAI's GPT models to classify and process vignettes based on the Cultural Proficiency Continuum. It stores chat history and classification data in a MySQL database hosted on AWS.

## Features
- Classifies vignettes based on the Cultural Proficiency Continuum
- Stores conversations and classifications in a MySQL database (AWS-hosted)
- Ensures concurrency with thread locks
- Tracks and logs latency for each request

---

## Requirements

- Python 3.8+
- MySQL Database (AWS-hosted)
- OpenAI API Key
- Git (for cloning the repository)

---

## Installation

1. **Clone the Repository:**
   ```bash
   git clone https://github.com/yourusername/repo-name.git
   cd repo-name
   ```

2. **Create a Virtual Environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows use: venv\Scripts\activate
   ```

3. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

---

## Environment Configuration

1. **Create a `.env` file in the root directory:**
   ```ini
   OPENAI_API=your_openai_api_key
   DB_HOST=aws-database-endpoint
   DB_USER=root
   DB_PASSWORD=your_password
   DB_NAME=chatbot_db
   ```

2. **Configure the `config.py` file to load environment variables:**
   ```python
   from dotenv import load_dotenv
   import os

   load_dotenv()

   OPENAI_API = os.getenv("OPENAI_API")
   DB_HOST = os.getenv("DB_HOST")
   DB_USER = os.getenv("DB_USER")
   DB_PASSWORD = os.getenv("DB_PASSWORD")
   DB_NAME = os.getenv("DB_NAME")
   ```

---

## Database Setup

1. **Create a MySQL Database and User (AWS-hosted):**
   ```sql
   CREATE DATABASE chatbot_db;
   CREATE USER 'root'@'%' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON chatbot_db.* TO 'root'@'%';
   FLUSH PRIVILEGES;
   ```

2. **Run the Schema (if provided):**
   ```bash
   mysql -u root -p -h aws-database-endpoint chatbot_db < schema.sql
   ```

---

## Running the Application

1. **Start the FastAPI Server:**
   ```bash
   uvicorn app.main:app --reload
   ```

2. **Access the API at:**
   ```
   http://127.0.0.1:8000
   ```

---

## Postman Collection

- A Postman collection is provided to help you interact with the API.
- The Postman collection includes:
  - How to send requests to the `/chat/` endpoint.
  - Example payloads for testing.
  - Detailed explanations on how to authenticate and interact with the chatbot.

### Importing the Collection

1. Download the Postman collection from the `postman` folder in this repository.
2. In Postman, go to **File -> Import** and upload the collection.
3. Use the provided environment to ensure the correct API endpoints and keys are configured.

---



**Disclaimer:**
- This project uses a MySQL database hosted on AWS. Ensure you have the correct permissions and credentials to access the database.
- The Postman collection guides you on how to interact with the API endpoints. Follow the instructions carefully for successful integration and testing. https://documenter.getpostman.com/view/44283392/2sB2j3DCPh
