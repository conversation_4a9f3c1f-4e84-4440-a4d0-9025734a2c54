import openai
from app.db.queries import user_exists, save_chat_to_db
from app.utils.tokenizer import count_tokens
from app.utils.error_handlers import (
    user_not_found_error, classification_error, no_class_match_error,
    empty_field_error, content_too_short_error, content_too_long_error,
    openai_api_error, database_error, server_error
)
from app.utils.logger import log_api_activity, log_classification
import time
import logging
from threading import Lock
from fastapi import HTTPException
from app.core.config import settings
from app.models.schemas import ClassificationRequest


AI_COPILOT_ID = "7"
conversation_context = {}
conversation_history = {}
lock = Lock()

classification_system_message = """
This is a framework to classify phrases into two categories based on their implications, values, or behaviors:

Unhealthy and Counter-Productive Behaviors
This category encompasses discourse and behaviors that are regressive, perpetuate inequities, and hinder the development of inclusive and equitable environments.

- Cultural Destructiveness:
  Definition: Actively seeking to eliminate or devalue other cultures, reinforcing systemic oppression.
  Examples:
  - Discriminatory policies that marginalize specific groups.
  - Dehumanizing practices that undermine respect for diversity.
  - Active hostility or antagonism toward inclusive efforts.

- Cultural Incapacity:
  Definition: Supporting or maintaining systems of inequality due to ignorance or lack of skills to engage with diversity.
  Examples:
  - Relying on stereotypes in decision-making.
  - Failing to accommodate the needs of marginalized groups.
  - Neglecting to address inequities within institutional practices.

- Cultural Blindness:
  Definition: Ignoring cultural differences and assuming that all individuals or groups have the same needs or experiences.
  Examples:
  - Adopting “colorblind” approaches that overlook systemic inequities.
  - Failing to recognize the distinct cultural needs of diverse groups.
  - Applying one-size-fits-all policies that neglect equity.

Healthy, Productive, and Transformational Behaviors
This category includes discourse and behaviors that are progressive, foster equity and inclusion, and lead to transformational outcomes in professional and human development.

- Cultural Pre-Competence:
  Definition: Acknowledging cultural differences and attempting to address inequities, though efforts may lack depth or consistency.
  Examples:
  - Initiating diversity training programs but not following through with systemic changes.
  - Engaging in surface-level diversity dialogues without structural impact.
  - Supporting efforts to increase awareness but failing to sustain long-term equity initiatives.

- Cultural Competence:
  Definition: Actively implementing policies, practices, and behaviors that value and adapt to cultural diversity.
  Examples:
  - Developing culturally responsive curricula or teaching strategies.
  - Creating inclusive workplace policies that address diversity needs.
  - Supporting equitable hiring and leadership practices.

- Cultural Proficiency:
  Definition: Championing equity and inclusion by advocating for systemic change and educating others to challenge inequities.
  Examples:
  - Leading transformational initiatives that drive social justice.
  - Elevating underrepresented voices and fostering systemic inclusivity.
  - Mentoring others in creating equity-focused solutions.

Your task is to classify the given phrase into one of the following two categories:
1. Healthy, Productive, and Transformational Behaviors
2. Unhealthy and Counter-Productive Behaviors

Your answer must follow this format:
Class [1: Healthy, Productive, and Transformational Behaviors / 2: Unhealthy and Counter-Productive Behaviors]
Explanation: Provide a clear and concise justification for your classification, referencing the definitions and examples where applicable.
"""

def handle_chat(request: ClassificationRequest):
    if not request.vignette or not request.vignette.strip():
        empty_field_error("vignette")

    if len(request.vignette) < 10:
        content_too_short_error("vignette", 10)

    if len(request.vignette) > 5000:
        content_too_long_error("vignette", 5000)

    if not request.user_id or not request.user_id.strip():
        empty_field_error("user_id")

    if not request.chat_id or not request.chat_id.strip():
        empty_field_error("chat_id")

    # Check if user exists
    if not user_exists(request.user_id):
        user_not_found_error(request.user_id)

    start_time = time.time()
    user_chat_key = f"{request.user_id}_{request.chat_id}_{AI_COPILOT_ID}"

    with lock:
        if user_chat_key not in conversation_history:
            conversation_history[user_chat_key] = []

    is_new_conversation = user_chat_key not in conversation_context

    openai.api_key = settings.OPENAI_API

    if is_new_conversation:
        classification_prompt = classification_system_message.format(prompt=request.vignette)
        response = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": classification_prompt},
                {"role": "user", "content": request.vignette}
            ]
        )
        reply = response.choices[0].message.content.strip()
        input_tokens = count_tokens(classification_prompt + request.vignette)
        output_tokens = count_tokens(reply)

        try:
            class_line, explanation = reply.split("Explanation:", 1)
            v_class = class_line.replace("Cultural Proficiency Continuum class: ", "").strip()

            # Standardize the format: convert "Class [1: X]" to "Class 1: X"
            if "[" in v_class and "]" in v_class:
                v_class = v_class.replace("[", "").replace("]", "")
        except ValueError:
            v_class = ""
            explanation = reply

        conversation_context[user_chat_key] = {"v_class": v_class, "explanation": explanation}
    else:
        # Get context from previous conversation
        messages = [{"role": "system", "content": "Continue the conversation naturally."}]

        for entry in conversation_history[user_chat_key]:
            messages.append({"role": "user", "content": entry["user"]})
            messages.append({"role": "assistant", "content": entry["bot"]})

        messages.append({"role": "user", "content": request.vignette})

        response = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages
        )
        reply = response.choices[0].message.content.strip()
        input_tokens = count_tokens(request.vignette)
        output_tokens = count_tokens(reply)

        v_class = conversation_context[user_chat_key]["v_class"]
        explanation = reply

    latency_ms = int((time.time() - start_time) * 1000)

    new_message = {
        "user": request.vignette,
        "bot": reply,
        "v_class": v_class,
        "explanation": explanation,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens,
        "latency": latency_ms,
        "ai_copilot_id": AI_COPILOT_ID
    }

    with lock:
        conversation_history[user_chat_key].append(new_message)

    save_chat_to_db(request.user_id, request.chat_id, AI_COPILOT_ID, new_message)
    return new_message
